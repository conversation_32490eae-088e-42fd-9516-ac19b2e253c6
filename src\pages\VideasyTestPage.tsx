import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import SecureVideoPlayer from '@/components/SecureVideoPlayer';
import { encodeVideoLinks } from '@/utils/videoSecurity';

export default function VideasyTestPage() {
  // Test Videasy URLs from the API documentation
  const testVideasyLinks = `
https://player.videasy.net/movie/299534
https://player.videasy.net/tv/1399/1/1
https://player.videasy.net/anime/21/1
https://player.videasy.net/movie/299534?color=8B5CF6&overlay=true
https://player.videasy.net/tv/1399/1/1?nextEpisode=true&episodeSelector=true
  `.trim();

  const encodedTestLinks = encodeVideoLinks(testVideasyLinks);

  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-8">
          
          {/* Header */}
          <div className="text-center space-y-4">
            <h1 className="text-3xl font-bold text-primary">Videasy Menu Fix Test Page</h1>
            <p className="text-muted-foreground">
              Testing Videasy embed menu sizing and positioning fixes
            </p>
          </div>

          {/* Test Player */}
          <Card className="bg-card/95 backdrop-blur-sm border-border/50 shadow-xl">
            <CardHeader>
              <CardTitle className="text-primary">Videasy Player Test</CardTitle>
              <p className="text-sm text-muted-foreground">
                This player should automatically detect Videasy URLs and apply proper menu scaling.
                The menu options should be properly sized and positioned within the player boundaries.
              </p>
            </CardHeader>
            <CardContent className="p-0">
              <SecureVideoPlayer
                encodedVideoLinks={encodedTestLinks}
                title="Videasy Menu Fix Test"
                showPlayerSelection={true}
                enableDynamicAspectRatio={false}
                forceMobileFriendly={false}
                className="w-full"
              />
            </CardContent>
          </Card>

          {/* Test Instructions */}
          <Card className="bg-card/95 backdrop-blur-sm border-border/50">
            <CardHeader>
              <CardTitle className="text-primary">Test Instructions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h3 className="font-semibold">What to Test:</h3>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  <li>Click on different player options to load Videasy embeds</li>
                  <li>Access the player's settings menu (gear icon)</li>
                  <li>Check subtitle options and other menu items</li>
                  <li>Verify menu elements are properly sized and positioned</li>
                  <li>Test on different screen sizes (mobile, tablet, desktop)</li>
                </ul>
              </div>
              
              <div className="space-y-2">
                <h3 className="font-semibold">Expected Behavior:</h3>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                  <li>Menu options should scale proportionally to the player size</li>
                  <li>Menu elements should not overflow the player boundaries</li>
                  <li>Settings and subtitle menus should be easily accessible</li>
                  <li>Player controls should remain functional</li>
                  <li>Fullscreen mode should work normally</li>
                </ul>
              </div>

              <div className="space-y-2">
                <h3 className="font-semibold">Test URLs Included:</h3>
                <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground font-mono">
                  <li>Movie: player.videasy.net/movie/299534</li>
                  <li>TV Show: player.videasy.net/tv/1399/1/1</li>
                  <li>Anime: player.videasy.net/anime/21/1</li>
                  <li>With Features: movie with color theme and overlay</li>
                  <li>TV with Controls: episode selector and next episode</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Technical Details */}
          <Card className="bg-card/95 backdrop-blur-sm border-border/50">
            <CardHeader>
              <CardTitle className="text-primary">Technical Implementation</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-semibold mb-2">CSS Scaling Applied:</h4>
                  <ul className="space-y-1 text-muted-foreground">
                    <li>Mobile (≤640px): scale(0.65)</li>
                    <li>Tablet (641px-1024px): scale(0.75)</li>
                    <li>Desktop (≥1025px): scale(0.85)</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Features:</h4>
                  <ul className="space-y-1 text-muted-foreground">
                    <li>Automatic Videasy detection</li>
                    <li>Responsive menu scaling</li>
                    <li>Overflow prevention</li>
                    <li>Transform-based positioning</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

        </div>
      </div>
    </div>
  );
}
