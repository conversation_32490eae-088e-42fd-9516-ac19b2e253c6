import { detectVideoPlatform } from '../utils/videoSecurity';

describe('Videasy Platform Detection', () => {
  test('should detect Videasy movie URLs', () => {
    const movieUrl = 'https://player.videasy.net/movie/299534';
    expect(detectVideoPlatform(movieUrl)).toBe('videasy');
  });

  test('should detect Videasy TV show URLs', () => {
    const tvUrl = 'https://player.videasy.net/tv/1399/1/1';
    expect(detectVideoPlatform(tvUrl)).toBe('videasy');
  });

  test('should detect Videasy anime URLs', () => {
    const animeUrl = 'https://player.videasy.net/anime/21/1';
    expect(detectVideoPlatform(animeUrl)).toBe('videasy');
  });

  test('should detect Videasy URLs with parameters', () => {
    const urlWithParams = 'https://player.videasy.net/movie/299534?color=8B5CF6&overlay=true';
    expect(detectVideoPlatform(urlWithParams)).toBe('videasy');
  });

  test('should not detect non-Videasy URLs as Videasy', () => {
    const youtubeUrl = 'https://www.youtube.com/embed/dQw4w9WgXcQ';
    expect(detectVideoPlatform(youtubeUrl)).not.toBe('videasy');
  });
});
